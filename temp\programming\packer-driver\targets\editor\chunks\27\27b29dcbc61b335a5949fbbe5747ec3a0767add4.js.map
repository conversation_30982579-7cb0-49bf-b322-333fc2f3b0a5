{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/weapon/Emitter.ts"], "names": ["_decorator", "Prefab", "Weapon", "ccclass", "property", "Emitter", "type", "displayName", "_internalEmitBullet", "canTrigger", "emitBullet", "onObjectInit", "onObjectDestroy", "unschedule", "onEnable", "startEmission", "onDisable", "stopEmission", "schedule", "frequency"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAkBC,MAAAA,M,OAAAA,M;;AAClBC,MAAAA,M,WAAAA,M;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;;yBAGRK,O,WADrBF,OAAO,CAAC,SAAD,C,UAGHC,QAAQ,CAAC;AAACE,QAAAA,IAAI,EAAEL,MAAP;AAAeM,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,2BAHb,MACsBF,OADtB;AAAA;AAAA,4BAC6C;AAAA;AAAA;;AAAA;;AAKzC;AALyC;;AASzC;AATyC;;AAazC;AAbyC;;AA8BzC;AACJ;AACA;AACA;AAjC6C,eAkCjCG,mBAlCiC,GAkCX,MAAY;AACtC,gBAAI,KAAKC,UAAL,EAAJ,EAAuB;AACnB,mBAAKC,UAAL;AACH;AACJ,WAtCwC;AAAA;;AAiBzCD,QAAAA,UAAU,GAAY;AAClB;AACA;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,UAAU,GAAS,CAE5B;;AAYD;AACUC,QAAAA,YAAY,GAAS,CAC3B;AACH;;AAESC,QAAAA,eAAe,GAAS;AAC9B;AACA,eAAKC,UAAL,CAAgB,KAAKL,mBAArB;AACH;;AAESM,QAAAA,QAAQ,GAAS;AACvB;AACA,eAAKC,aAAL;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AACA,eAAKC,YAAL;AACH;AAED;AACJ;AACA;AACA;;;AACcF,QAAAA,aAAa,GAAS;AAC5B,eAAKG,QAAL,CAAc,KAAKV,mBAAnB,EAAwC,KAAKW,SAA7C;AACH;AAED;AACJ;AACA;AACA;;;AACcF,QAAAA,YAAY,GAAS;AAC3B,eAAKJ,UAAL,CAAgB,KAAKL,mBAArB;AACH;;AA1EwC,O;;;;;iBAGlB,I;;gFAGtBJ,Q;;;;;iBACe,C;;0FAGfA,Q;;;;;iBACyB,C;;oFAGzBA,Q;;;;;iBACmB,C", "sourcesContent": ["import { _decorator, Node, Prefab } from 'cc';\r\nimport { Weapon } from 'Weapon';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('Emitter')\r\nexport abstract class Emitter extends Weapon {\r\n\r\n    @property({type: Prefab, displayName: \"Bullet Prefab\"})\r\n    bulletPrefab: Prefab = null;\r\n\r\n    // 发射条数\r\n    @property\r\n    count: number = 1;\r\n\r\n    // 子弹速度乘数\r\n    @property\r\n    speedMultiplier: number = 1;\r\n\r\n    // 频率(间隔多少秒发射一次)\r\n    @property\r\n    frequency: number = 1;\r\n\r\n    canTrigger(): boolean {\r\n        // 检查是否可以触发发射\r\n        // Override this method in subclasses to add custom trigger conditions\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * TODO: implement bullet emission logic in subclasses\r\n     */\r\n    protected emitBullet(): void {\r\n\r\n    }\r\n\r\n    /**\r\n     * Internal method that wraps emitBullet for scheduling\r\n     * This is a concrete method that can be safely scheduled\r\n     */\r\n    private _internalEmitBullet = (): void => {\r\n        if (this.canTrigger()) {\r\n            this.emitBullet();\r\n        }\r\n    }\r\n\r\n    // Implementation of CObject abstract methods\r\n    protected onObjectInit(): void {\r\n        // Override in subclasses if needed\r\n    }\r\n\r\n    protected onObjectDestroy(): void {\r\n        // Clean up any scheduled callbacks\r\n        this.unschedule(this._internalEmitBullet);\r\n    }\r\n\r\n    protected onEnable(): void {\r\n        // Start the emission schedule\r\n        this.startEmission();\r\n    }\r\n\r\n    protected onDisable(): void {\r\n        // Stop the emission schedule\r\n        this.stopEmission();\r\n    }\r\n\r\n    /**\r\n     * Start the emission schedule\r\n     * This method can be overridden by subclasses if needed\r\n     */\r\n    protected startEmission(): void {\r\n        this.schedule(this._internalEmitBullet, this.frequency);\r\n    }\r\n\r\n    /**\r\n     * Stop the emission schedule\r\n     * This method can be overridden by subclasses if needed\r\n     */\r\n    protected stopEmission(): void {\r\n        this.unschedule(this._internalEmitBullet);\r\n    }\r\n}\r\n"]}