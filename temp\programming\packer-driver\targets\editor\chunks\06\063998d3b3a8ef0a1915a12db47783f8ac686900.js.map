{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts"], "names": ["EventCondition", "EventAction", "_decorator", "CObject", "ccclass", "property", "eMoveType", "Bullet", "onObjectInit", "onObjectDestroy", "update", "deltaTime"], "mappings": ";;;oGAUaA,c,EAIAC,W;;;;;;;;;;;;;;;;;;AAdJC,MAAAA,U,OAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;AAEzBI,MAAAA,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;QAAAA,S;;gCAMQN,c,GAAN,MAAMA,cAAN,CAAqB,E;;6BAIfC,W,GAAN,MAAMA,WAAN,CAAkB,E,GAIzB;AAEA;AACA;;;wBAGaM,M,WADZH,OAAO,CAAC,QAAD,C,gBAAR,MACaG,MADb;AAAA;AAAA,8BACoC;AAEtBC,QAAAA,YAAY,GAAS,CAE9B;;AACSC,QAAAA,eAAe,GAAS,CAEjC;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AAX+B,O", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\r\nimport { CObject } from '../base/Object';\r\nconst { ccclass, property } = _decorator;\r\n\r\nenum eMoveType {\r\n    Straight, // 直线\r\n    Curve,    // 曲线 \r\n    Seek,     // 追踪\r\n}\r\n\r\nexport class EventCondition {\r\n\r\n}\r\n\r\nexport class EventAction {\r\n\r\n}\r\n\r\n// 子弹 Bullet 伤害计算 \r\n\r\n// Weapon -> 发射器, 喷火, 技能武器, 激光\r\n// WeaponSlot -> SetWeapon\r\n\r\n@ccclass('Bullet')\r\nexport class Bullet extends CObject {\r\n\r\n    protected onObjectInit(): void {\r\n        \r\n    }\r\n    protected onObjectDestroy(): void {\r\n        \r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        \r\n    }\r\n}\r\n\r\n\r\n"]}