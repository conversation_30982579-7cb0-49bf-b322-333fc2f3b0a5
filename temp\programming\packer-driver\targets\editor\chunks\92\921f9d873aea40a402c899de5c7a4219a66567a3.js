System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Emitter, _dec, _class, _class2, _descriptor, _descriptor2, _descriptor3, _crd, ccclass, property, EmitterArc;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "./Emitter", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      Emitter = _unresolved_2.Emitter;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "93e575huyBPT6bORtc6obQb", "EmitterArc", undefined);

      __checkObsolete__(['_decorator']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("EmitterArc", EmitterArc = (_dec = ccclass('EmitterArc'), _dec(_class = (_class2 = class EmitterArc extends (_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
        error: Error()
      }), Emitter) : Emitter) {
        constructor(...args) {
          super(...args);

          // 发射角度
          _initializerDefineProperty(this, "angle", _descriptor, this);

          // 发射弧度
          _initializerDefineProperty(this, "arc", _descriptor2, this);

          // 发射半径
          _initializerDefineProperty(this, "radius", _descriptor3, this);
        }

        /**
         * Implementation of emitBullet for arc-based emission
         */
        emitBullet() {
          if (!this.bulletPrefab || this.count <= 0) {
            return;
          } // TODO: Implement bullet emission logic
          // This will be implemented when the bullet system is ready


          console.log(`EmitterArc: Emitting ${this.count} bullets in arc pattern`);

          for (let i = 0; i < this.count; i++) {
            const direction = this.getDirection(i); // TODO: Create and configure bullet instance

            console.log(`Bullet ${i}: direction (${direction.x.toFixed(2)}, ${direction.y.toFixed(2)})`);
          }
        }
        /**
         * Calculate the direction for a bullet at the given index
         * @param index The index of the bullet (0 to count-1)
         * @returns Direction vector {x, y}
         */


        getDirection(index) {
          // 计算发射方向
          const angleOffset = this.count > 1 ? this.arc / (this.count - 1) * index - this.arc / 2 : 0;
          const radian = (this.angle + angleOffset) * (Math.PI / 180);
          return {
            x: Math.cos(radian),
            y: Math.sin(radian)
          };
        }
        /**
         * Get the spawn position for a bullet at the given index
         * @param index The index of the bullet (0 to count-1)
         * @returns Position offset from emitter center
         */


        getSpawnPosition(index) {
          if (this.radius <= 0) {
            return {
              x: 0,
              y: 0
            };
          }

          const direction = this.getDirection(index);
          return {
            x: direction.x * this.radius,
            y: direction.y * this.radius
          };
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "angle", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 90;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "arc", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "radius", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=921f9d873aea40a402c899de5c7a4219a66567a3.js.map