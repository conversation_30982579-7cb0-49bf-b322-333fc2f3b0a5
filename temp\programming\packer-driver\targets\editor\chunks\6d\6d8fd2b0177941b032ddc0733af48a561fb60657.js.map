{"version": 3, "sources": ["cce:/internal/x/prerequisite-imports"], "names": ["requests", "request", "_err"], "mappings": ";;;;;;AACA;AAEA,YAAM,CAAC,YAAY;AACf,cAAMA,QAAQ,GAAG,CAAC,uCAAD,EAA4J,uCAA5J,EAA4T,uCAA5T,EAAge,uCAAhe,EAAioB,uCAAjoB,EAA4xB,uCAA5xB,EAAg7B,uCAAh7B,EAA4hC,uCAA5hC,EAAuoC,uCAAvoC,EAAwuC,uCAAxuC,EAA+0C,wCAA/0C,EAAi7C,wCAAj7C,EAAyhD,wCAAzhD,EAA8nD,wCAA9nD,EAAiuD,wCAAjuD,EAAm0D,wCAAn0D,EAAw6D,wCAAx6D,EAAygE,wCAAzgE,EAAinE,wCAAjnE,EAAotE,wCAAptE,EAA6zE,wCAA7zE,EAA66E,wCAA76E,EAAoiF,wCAApiF,EAAqpF,wCAArpF,EAAqwF,wCAArwF,EAAs3F,wCAAt3F,EAA8+F,wCAA9+F,EAAimG,wCAAjmG,EAAgtG,wCAAhtG,EAAg0G,wCAAh0G,EAA86G,wCAA96G,EAAuhH,wCAAvhH,EAAmoH,wCAAnoH,EAAyvH,wCAAzvH,EAAu2H,wCAAv2H,EAAq9H,wCAAr9H,EAA4kI,wCAA5kI,EAA0rI,wCAA1rI,EAAuyI,wCAAvyI,EAA+4I,wCAA/4I,EAAmgJ,wCAAngJ,EAAynJ,wCAAznJ,EAAyuJ,wCAAzuJ,EAA+1J,wCAA/1J,EAAg9J,wCAAh9J,EAAokK,wCAApkK,EAAorK,wCAAprK,EAAwyK,wCAAxyK,EAA44K,wCAA54K,EAAw+K,wCAAx+K,EAA8kL,wCAA9kL,EAA4qL,wCAA5qL,EAAkxL,wCAAlxL,CAAjB;;AACA,aAAK,MAAMC,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,cAAI;AACA,kBAAMC,OAAO,EAAb;AACH,WAFD,CAEE,OAAOC,IAAP,EAAa,CACX;AACH;AACJ;AACJ,OATK,GAAN", "sourcesContent": ["\n// Auto generated represents the prerequisite imports of project modules.\n\nawait (async () => {\n    const requests = [() => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/EmitterArcGizmo.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoDrawer.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoUtils.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/index.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/Object.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/weapon/Bullet.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/weapon/BulletSystem.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/weapon/Emitter.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/weapon/EmitterArc.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/weapon/Weapon.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/weapon/WeaponSlot.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts\")];\n    for (const request of requests) {\n        try {\n            await request();\n        } catch (_err) {\n            // The error should have been caught by executor.\n        }\n    }\n})();\n    "]}