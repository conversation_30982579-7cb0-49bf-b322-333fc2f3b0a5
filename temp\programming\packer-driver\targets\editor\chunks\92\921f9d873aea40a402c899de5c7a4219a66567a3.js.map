{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/weapon/EmitterArc.ts"], "names": ["_decorator", "Emitter", "ccclass", "property", "EmitterArc", "emitBullet", "bulletPrefab", "count", "console", "log", "i", "direction", "getDirection", "x", "toFixed", "y", "index", "angleOffset", "arc", "radian", "angle", "Math", "PI", "cos", "sin", "getSpawnPosition", "radius"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;4BAGjBI,U,WADZF,OAAO,CAAC,YAAD,C,2BAAR,MACaE,UADb;AAAA;AAAA,8BACwC;AAAA;AAAA;;AAEpC;AAFoC;;AAMpC;AANoC;;AAUpC;AAVoC;AAAA;;AAcpC;AACJ;AACA;AACIC,QAAAA,UAAU,GAAS;AACf,cAAI,CAAC,KAAKC,YAAN,IAAsB,KAAKC,KAAL,IAAc,CAAxC,EAA2C;AACvC;AACH,WAHc,CAKf;AACA;;;AACAC,UAAAA,OAAO,CAACC,GAAR,CAAa,wBAAuB,KAAKF,KAAM,yBAA/C;;AAEA,eAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKH,KAAzB,EAAgCG,CAAC,EAAjC,EAAqC;AACjC,kBAAMC,SAAS,GAAG,KAAKC,YAAL,CAAkBF,CAAlB,CAAlB,CADiC,CAEjC;;AACAF,YAAAA,OAAO,CAACC,GAAR,CAAa,UAASC,CAAE,gBAAeC,SAAS,CAACE,CAAV,CAAYC,OAAZ,CAAoB,CAApB,CAAuB,KAAIH,SAAS,CAACI,CAAV,CAAYD,OAAZ,CAAoB,CAApB,CAAuB,GAAzF;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIF,QAAAA,YAAY,CAACI,KAAD,EAA0C;AAClD;AACA,gBAAMC,WAAW,GAAG,KAAKV,KAAL,GAAa,CAAb,GAAkB,KAAKW,GAAL,IAAY,KAAKX,KAAL,GAAa,CAAzB,CAAD,GAAgCS,KAAhC,GAAwC,KAAKE,GAAL,GAAW,CAApE,GAAwE,CAA5F;AACA,gBAAMC,MAAM,GAAG,CAAC,KAAKC,KAAL,GAAaH,WAAd,KAA8BI,IAAI,CAACC,EAAL,GAAU,GAAxC,CAAf;AACA,iBAAO;AACHT,YAAAA,CAAC,EAAEQ,IAAI,CAACE,GAAL,CAASJ,MAAT,CADA;AAEHJ,YAAAA,CAAC,EAAEM,IAAI,CAACG,GAAL,CAASL,MAAT;AAFA,WAAP;AAIH;AAED;AACJ;AACA;AACA;AACA;;;AACIM,QAAAA,gBAAgB,CAACT,KAAD,EAA0C;AACtD,cAAI,KAAKU,MAAL,IAAe,CAAnB,EAAsB;AAClB,mBAAO;AAAEb,cAAAA,CAAC,EAAE,CAAL;AAAQE,cAAAA,CAAC,EAAE;AAAX,aAAP;AACH;;AAED,gBAAMJ,SAAS,GAAG,KAAKC,YAAL,CAAkBI,KAAlB,CAAlB;AACA,iBAAO;AACHH,YAAAA,CAAC,EAAEF,SAAS,CAACE,CAAV,GAAc,KAAKa,MADnB;AAEHX,YAAAA,CAAC,EAAEJ,SAAS,CAACI,CAAV,GAAc,KAAKW;AAFnB,WAAP;AAIH;;AA/DmC,O,wEAGnCvB,Q;;;;;iBACe,E;;8EAGfA,Q;;;;;iBACa,C;;iFAGbA,Q;;;;;iBACgB,C", "sourcesContent": ["import { _decorator } from 'cc';\r\nimport { Emitter } from './Emitter';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EmitterArc')\r\nexport class EmitterArc extends Emitter {\r\n\r\n    // 发射角度\r\n    @property\r\n    angle: number = 90;\r\n\r\n    // 发射弧度\r\n    @property\r\n    arc: number = 0;\r\n\r\n    // 发射半径\r\n    @property\r\n    radius: number = 0;\r\n\r\n    /**\r\n     * Implementation of emitBullet for arc-based emission\r\n     */\r\n    emitBullet(): void {\r\n        if (!this.bulletPrefab || this.count <= 0) {\r\n            return;\r\n        }\r\n\r\n        // TODO: Implement bullet emission logic\r\n        // This will be implemented when the bullet system is ready\r\n        console.log(`EmitterArc: Emitting ${this.count} bullets in arc pattern`);\r\n\r\n        for (let i = 0; i < this.count; i++) {\r\n            const direction = this.getDirection(i);\r\n            // TODO: Create and configure bullet instance\r\n            console.log(`Bullet ${i}: direction (${direction.x.toFixed(2)}, ${direction.y.toFixed(2)})`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Calculate the direction for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Direction vector {x, y}\r\n     */\r\n    getDirection(index: number): { x: number, y: number } {\r\n        // 计算发射方向\r\n        const angleOffset = this.count > 1 ? (this.arc / (this.count - 1)) * index - this.arc / 2 : 0;\r\n        const radian = (this.angle + angleOffset) * (Math.PI / 180);\r\n        return {\r\n            x: Math.cos(radian),\r\n            y: Math.sin(radian)\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Get the spawn position for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Position offset from emitter center\r\n     */\r\n    getSpawnPosition(index: number): { x: number, y: number } {\r\n        if (this.radius <= 0) {\r\n            return { x: 0, y: 0 };\r\n        }\r\n\r\n        const direction = this.getDirection(index);\r\n        return {\r\n            x: direction.x * this.radius,\r\n            y: direction.y * this.radius\r\n        };\r\n    }\r\n}"]}