System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Enum, World, WorldState, WorldInitializeData, GameMode, DifficultyLevel, LevelSystem, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _crd, ccclass, property, Bootstrap;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfWorld(extras) {
    _reporterNs.report("World", "./base/World", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWorldState(extras) {
    _reporterNs.report("WorldState", "./base/World", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIWorldInitializeData(extras) {
    _reporterNs.report("IWorldInitializeData", "./WorldInitializeData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWorldInitializeData(extras) {
    _reporterNs.report("WorldInitializeData", "./WorldInitializeData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameMode(extras) {
    _reporterNs.report("GameMode", "./WorldInitializeData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDifficultyLevel(extras) {
    _reporterNs.report("DifficultyLevel", "./WorldInitializeData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSystem(extras) {
    _reporterNs.report("System", "./base/System", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelSystem(extras) {
    _reporterNs.report("LevelSystem", "./level/LevelSystem", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Enum = _cc.Enum;
    }, function (_unresolved_2) {
      World = _unresolved_2.World;
      WorldState = _unresolved_2.WorldState;
    }, function (_unresolved_3) {
      WorldInitializeData = _unresolved_3.WorldInitializeData;
      GameMode = _unresolved_3.GameMode;
      DifficultyLevel = _unresolved_3.DifficultyLevel;
    }, function (_unresolved_4) {
      LevelSystem = _unresolved_4.LevelSystem;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "65d59N285pDZY3XJQwdcUDb", "Bootstrap", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'Enum']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * Bootstrap component - the main entry point for the World system
       * Extends cc.Component to integrate with Cocos Creator's lifecycle
       * Creates and manages the World instance and drives the world update loop
       */

      _export("Bootstrap", Bootstrap = (_dec = ccclass("Bootstrap"), _dec2 = property({
        displayName: "Auto Start",
        tooltip: "Automatically start the world on component start"
      }), _dec3 = property({
        displayName: "Game Mode",
        type: Enum(_crd && GameMode === void 0 ? (_reportPossibleCrUseOfGameMode({
          error: Error()
        }), GameMode) : GameMode),
        tooltip: "The game mode to initialize"
      }), _dec4 = property({
        displayName: "Level ID",
        tooltip: "The level identifier to load"
      }), _dec5 = property({
        displayName: "Difficulty",
        type: Enum(_crd && DifficultyLevel === void 0 ? (_reportPossibleCrUseOfDifficultyLevel({
          error: Error()
        }), DifficultyLevel) : DifficultyLevel),
        tooltip: "The difficulty level"
      }), _dec6 = property({
        displayName: "Random Seed",
        tooltip: "Random seed for deterministic gameplay (0 = use current time)"
      }), _dec7 = property({
        displayName: "Time Scale",
        range: [0.1, 5.0, 0.1],
        tooltip: "Time scale for the world (1.0 = normal speed)"
      }), _dec8 = property({
        displayName: "Enable Debug",
        tooltip: "Enable debug features"
      }), _dec9 = property({
        displayName: "Max Particles",
        tooltip: "Maximum number of particles"
      }), _dec(_class = (_class2 = class Bootstrap extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "autoStart", _descriptor, this);

          _initializerDefineProperty(this, "gameMode", _descriptor2, this);

          _initializerDefineProperty(this, "levelId", _descriptor3, this);

          _initializerDefineProperty(this, "difficulty", _descriptor4, this);

          _initializerDefineProperty(this, "randomSeed", _descriptor5, this);

          _initializerDefineProperty(this, "timeScale", _descriptor6, this);

          _initializerDefineProperty(this, "enableDebug", _descriptor7, this);

          _initializerDefineProperty(this, "maxParticles", _descriptor8, this);

          // World instance
          this._world = null;
          // Initialization data
          this._initializeData = null;
          // State tracking
          this._isInitialized = false;
          this._isPaused = false;
        }

        /**
         * Component lifecycle - called when component is loaded
         */
        onLoad() {
          console.log("Bootstrap: Component loaded"); // Create world instance

          this._world = new (_crd && World === void 0 ? (_reportPossibleCrUseOfWorld({
            error: Error()
          }), World) : World)();

          this._world.registerSystem(new (_crd && LevelSystem === void 0 ? (_reportPossibleCrUseOfLevelSystem({
            error: Error()
          }), LevelSystem) : LevelSystem)()); // Set up world event callbacks


          this._world.setOnStateChanged(this._onWorldStateChanged.bind(this));

          this._world.setOnError(this._onWorldError.bind(this)); // Prepare initialization data


          this._prepareInitializeData();
        }
        /**
         * Component lifecycle - called when component starts
         */


        start() {
          console.log("Bootstrap: Component started");

          if (this.autoStart) {
            this.startWorld();
          }
        }
        /**
         * Component lifecycle - called every frame
         */


        update(deltaTime) {
          if (!this._world || !this._isInitialized || this._isPaused) {
            return;
          } // Update the world


          this._world.update(deltaTime);
        }
        /**
         * Component lifecycle - called after all updates
         */


        lateUpdate() {
          if (!this._world || !this._isInitialized || this._isPaused) {
            return;
          } // Late update the world


          this._world.lateUpdate(0); // LateUpdate doesn't need deltaTime in this context

        }
        /**
         * Component lifecycle - called when component is destroyed
         */


        onDestroy() {
          console.log("Bootstrap: Component destroyed");

          if (this._world) {
            this._world.destroy();

            this._world = null;
          }

          this._isInitialized = false;
        }
        /**
         * Start the world with the configured settings
         * @returns Promise that resolves when the world is started
         */


        async startWorld() {
          if (!this._world || this._isInitialized) {
            console.warn("Bootstrap: Cannot start world - already initialized or no world instance");
            return false;
          }

          if (!this._initializeData) {
            console.error("Bootstrap: Cannot start world - no initialization data");
            return false;
          }

          console.log("Bootstrap: Starting world...");
          const success = await this._world.initialize(this._initializeData);

          if (success) {
            this._isInitialized = true;
            console.log("Bootstrap: World started successfully");
          } else {
            console.error("Bootstrap: Failed to start world");
          }

          return success;
        }
        /**
         * Stop the world
         */


        stopWorld() {
          if (!this._world || !this._isInitialized) {
            console.warn("Bootstrap: Cannot stop world - not initialized");
            return;
          }

          console.log("Bootstrap: Stopping world...");

          this._world.stop();

          this._isInitialized = false;
        }
        /**
         * Pause the world
         */


        pauseWorld() {
          if (!this._world || !this._isInitialized) {
            console.warn("Bootstrap: Cannot pause world - not initialized");
            return;
          }

          this._world.pause();

          this._isPaused = true;
          console.log("Bootstrap: World paused");
        }
        /**
         * Resume the world
         */


        resumeWorld() {
          if (!this._world || !this._isInitialized) {
            console.warn("Bootstrap: Cannot resume world - not initialized");
            return;
          }

          this._world.start();

          this._isPaused = false;
          console.log("Bootstrap: World resumed");
        }
        /**
         * Register a system to the world
         * @param system The system to register
         * @returns true if registration was successful
         */


        registerSystem(system) {
          if (!this._world) {
            console.warn("Bootstrap: Cannot register system - no world instance");
            return false;
          }

          return this._world.registerSystem(system);
        }
        /**
         * Unregister a system from the world
         * @param systemConstructor The constructor of the system to unregister
         * @returns true if unregistration was successful
         */


        unregisterSystem(systemConstructor) {
          if (!this._world) {
            console.warn("Bootstrap: Cannot unregister system - no world instance");
            return false;
          }

          return this._world.unregisterSystem(systemConstructor);
        }
        /**
         * Get a system by type
         * @param systemConstructor The constructor of the system to get
         * @returns The system instance or null if not found
         */


        getSystem(systemConstructor) {
          if (!this._world) {
            console.warn("Bootstrap: Cannot get system - no world instance");
            return null;
          }

          return this._world.getSystem(systemConstructor);
        }
        /**
         * Get the world instance
         * @returns The world instance or null if not created
         */


        getWorld() {
          return this._world;
        }
        /**
         * Check if the world is initialized
         * @returns true if the world is initialized
         */


        isWorldInitialized() {
          return this._isInitialized;
        }
        /**
         * Check if the world is paused
         * @returns true if the world is paused
         */


        isWorldPaused() {
          return this._isPaused;
        }
        /**
         * Get the current world state
         * @returns The current world state or null if no world
         */


        getWorldState() {
          return this._world ? this._world.getState() : null;
        }
        /**
         * Prepare the initialization data from component properties
         */


        _prepareInitializeData() {
          // Create configuration object
          const config = {
            modeId: this.gameMode,
            levelId: this.levelId,
            difficulty: this.difficulty,
            randomSeed: this.randomSeed > 0 ? this.randomSeed : Date.now(),
            physicsConfig: {
              gravity: -9.8,
              timeScale: this.timeScale,
              maxVelocity: 1000,
              enableCollision: true
            },
            renderConfig: {
              enableParticles: true,
              maxParticles: this.maxParticles,
              enablePostProcessing: true,
              renderScale: 1.0
            },
            debugFlags: {
              enableDebugDraw: this.enableDebug,
              showCollisionBounds: this.enableDebug,
              showPerformanceStats: this.enableDebug,
              logSystemUpdates: this.enableDebug
            }
          }; // Create initialization data with configuration

          this._initializeData = new (_crd && WorldInitializeData === void 0 ? (_reportPossibleCrUseOfWorldInitializeData({
            error: Error()
          }), WorldInitializeData) : WorldInitializeData)(config);
          console.log("Bootstrap: Initialization data prepared", this._initializeData);
        }
        /**
         * Handle world state changes
         * @param oldState The previous world state
         * @param newState The new world state
         */


        _onWorldStateChanged(oldState, newState) {
          console.log(`Bootstrap: World state changed from ${oldState} to ${newState}`); // Handle specific state transitions

          switch (newState) {
            case (_crd && WorldState === void 0 ? (_reportPossibleCrUseOfWorldState({
              error: Error()
            }), WorldState) : WorldState).RUNNING:
              this._isPaused = false;
              break;

            case (_crd && WorldState === void 0 ? (_reportPossibleCrUseOfWorldState({
              error: Error()
            }), WorldState) : WorldState).PAUSED:
              this._isPaused = true;
              break;

            case (_crd && WorldState === void 0 ? (_reportPossibleCrUseOfWorldState({
              error: Error()
            }), WorldState) : WorldState).STOPPED:
              this._isInitialized = false;
              this._isPaused = false;
              break;

            case (_crd && WorldState === void 0 ? (_reportPossibleCrUseOfWorldState({
              error: Error()
            }), WorldState) : WorldState).ERROR:
              this._isInitialized = false;
              this._isPaused = false;
              break;
          }
        }
        /**
         * Handle world errors
         * @param error The error that occurred
         */


        _onWorldError(error) {
          console.error("Bootstrap: World error occurred:", error); // You can add custom error handling here
          // For example, show error UI, restart world, etc.
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "autoStart", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return true;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "gameMode", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return (_crd && GameMode === void 0 ? (_reportPossibleCrUseOfGameMode({
            error: Error()
          }), GameMode) : GameMode).STORY;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "levelId", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return "level_001";
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "difficulty", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return (_crd && DifficultyLevel === void 0 ? (_reportPossibleCrUseOfDifficultyLevel({
            error: Error()
          }), DifficultyLevel) : DifficultyLevel).NORMAL;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "randomSeed", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "timeScale", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1.0;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "enableDebug", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "maxParticles", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1000;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=31f23b42610b243216ebc78eb807405366313241.js.map