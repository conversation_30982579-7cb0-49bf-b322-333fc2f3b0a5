{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts"], "names": ["_decorator", "Component", "Enum", "World", "WorldState", "WorldInitializeData", "GameMode", "DifficultyLevel", "LevelSystem", "ccclass", "property", "Bootstrap", "displayName", "tooltip", "type", "range", "_world", "_initializeData", "_isInitialized", "_isPaused", "onLoad", "console", "log", "registerSystem", "setOnStateChanged", "_onWorldStateChanged", "bind", "setOnError", "_onWorldError", "_prepareInitializeData", "start", "autoStart", "startWorld", "update", "deltaTime", "lateUpdate", "onDestroy", "destroy", "warn", "error", "success", "initialize", "stopWorld", "stop", "pauseWorld", "pause", "resumeWorld", "system", "unregisterSystem", "systemConstructor", "getSystem", "getWorld", "isWorldInitialized", "isWorldPaused", "getWorldState", "getState", "config", "modeId", "gameMode", "levelId", "difficulty", "randomSeed", "Date", "now", "physicsConfig", "gravity", "timeScale", "maxVelocity", "enableCollision", "renderConfig", "enableParticles", "maxParticles", "enablePostProcessing", "renderScale", "debugFlags", "enableDebugDraw", "enableDebug", "showCollisionBounds", "showPerformanceStats", "logSystemUpdates", "oldState", "newState", "RUNNING", "PAUSED", "STOPPED", "ERROR", "STORY", "NORMAL"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,I,OAAAA,I;;AAC7BC,MAAAA,K,iBAAAA,K;AAAOC,MAAAA,U,iBAAAA,U;;AACeC,MAAAA,mB,iBAAAA,mB;AAAqBC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,e,iBAAAA,e;;AAErDC,MAAAA,W,iBAAAA,W;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;AAE9B;AACA;AACA;AACA;AACA;;2BAEaW,S,WADZF,OAAO,CAAC,WAAD,C,UAGHC,QAAQ,CAAC;AAAEE,QAAAA,WAAW,EAAE,YAAf;AAA6BC,QAAAA,OAAO,EAAE;AAAtC,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,WAAW,EAAE,WAAf;AAA4BE,QAAAA,IAAI,EAAEZ,IAAI;AAAA;AAAA,iCAAtC;AAAkDW,QAAAA,OAAO,EAAE;AAA3D,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,WAAW,EAAE,UAAf;AAA2BC,QAAAA,OAAO,EAAE;AAApC,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,WAAW,EAAE,YAAf;AAA6BE,QAAAA,IAAI,EAAEZ,IAAI;AAAA;AAAA,+CAAvC;AAA0DW,QAAAA,OAAO,EAAE;AAAnE,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,WAAW,EAAE,aAAf;AAA8BC,QAAAA,OAAO,EAAE;AAAvC,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,WAAW,EAAE,YAAf;AAA6BG,QAAAA,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAApC;AAAqDF,QAAAA,OAAO,EAAE;AAA9D,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,WAAW,EAAE,cAAf;AAA+BC,QAAAA,OAAO,EAAE;AAAxC,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,WAAW,EAAE,eAAf;AAAgCC,QAAAA,OAAO,EAAE;AAAzC,OAAD,C,2BAxBb,MACaF,SADb,SAC+BV,SAD/B,CACyC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AA0BrC;AA1BqC,eA2B7Be,MA3B6B,GA2BN,IA3BM;AA6BrC;AA7BqC,eA8B7BC,eA9B6B,GA8BiB,IA9BjB;AAgCrC;AAhCqC,eAiC7BC,cAjC6B,GAiCH,KAjCG;AAAA,eAkC7BC,SAlC6B,GAkCR,KAlCQ;AAAA;;AAoCrC;AACJ;AACA;AACIC,QAAAA,MAAM,GAAS;AACXC,UAAAA,OAAO,CAACC,GAAR,CAAY,6BAAZ,EADW,CAGX;;AACA,eAAKN,MAAL,GAAc;AAAA;AAAA,+BAAd;;AACA,eAAKA,MAAL,CAAYO,cAAZ,CAA2B;AAAA;AAAA,2CAA3B,EALW,CAOX;;;AACA,eAAKP,MAAL,CAAYQ,iBAAZ,CAA8B,KAAKC,oBAAL,CAA0BC,IAA1B,CAA+B,IAA/B,CAA9B;;AACA,eAAKV,MAAL,CAAYW,UAAZ,CAAuB,KAAKC,aAAL,CAAmBF,IAAnB,CAAwB,IAAxB,CAAvB,EATW,CAWX;;;AACA,eAAKG,sBAAL;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,KAAK,GAAS;AACVT,UAAAA,OAAO,CAACC,GAAR,CAAY,8BAAZ;;AAEA,cAAI,KAAKS,SAAT,EAAoB;AAChB,iBAAKC,UAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,MAAM,CAACC,SAAD,EAA0B;AAC5B,cAAI,CAAC,KAAKlB,MAAN,IAAgB,CAAC,KAAKE,cAAtB,IAAwC,KAAKC,SAAjD,EAA4D;AACxD;AACH,WAH2B,CAK5B;;;AACA,eAAKH,MAAL,CAAYiB,MAAZ,CAAmBC,SAAnB;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,UAAU,GAAS;AACf,cAAI,CAAC,KAAKnB,MAAN,IAAgB,CAAC,KAAKE,cAAtB,IAAwC,KAAKC,SAAjD,EAA4D;AACxD;AACH,WAHc,CAKf;;;AACA,eAAKH,MAAL,CAAYmB,UAAZ,CAAuB,CAAvB,EANe,CAMY;;AAC9B;AAED;AACJ;AACA;;;AACIC,QAAAA,SAAS,GAAS;AACdf,UAAAA,OAAO,CAACC,GAAR,CAAY,gCAAZ;;AAEA,cAAI,KAAKN,MAAT,EAAiB;AACb,iBAAKA,MAAL,CAAYqB,OAAZ;;AACA,iBAAKrB,MAAL,GAAc,IAAd;AACH;;AAED,eAAKE,cAAL,GAAsB,KAAtB;AACH;AAED;AACJ;AACA;AACA;;;AAC2B,cAAVc,UAAU,GAAqB;AACxC,cAAI,CAAC,KAAKhB,MAAN,IAAgB,KAAKE,cAAzB,EAAyC;AACrCG,YAAAA,OAAO,CAACiB,IAAR,CAAa,0EAAb;AACA,mBAAO,KAAP;AACH;;AAED,cAAI,CAAC,KAAKrB,eAAV,EAA2B;AACvBI,YAAAA,OAAO,CAACkB,KAAR,CAAc,wDAAd;AACA,mBAAO,KAAP;AACH;;AAEDlB,UAAAA,OAAO,CAACC,GAAR,CAAY,8BAAZ;AAEA,gBAAMkB,OAAO,GAAG,MAAM,KAAKxB,MAAL,CAAYyB,UAAZ,CAAuB,KAAKxB,eAA5B,CAAtB;;AACA,cAAIuB,OAAJ,EAAa;AACT,iBAAKtB,cAAL,GAAsB,IAAtB;AACAG,YAAAA,OAAO,CAACC,GAAR,CAAY,uCAAZ;AACH,WAHD,MAGO;AACHD,YAAAA,OAAO,CAACkB,KAAR,CAAc,kCAAd;AACH;;AAED,iBAAOC,OAAP;AACH;AAED;AACJ;AACA;;;AACWE,QAAAA,SAAS,GAAS;AACrB,cAAI,CAAC,KAAK1B,MAAN,IAAgB,CAAC,KAAKE,cAA1B,EAA0C;AACtCG,YAAAA,OAAO,CAACiB,IAAR,CAAa,gDAAb;AACA;AACH;;AAEDjB,UAAAA,OAAO,CAACC,GAAR,CAAY,8BAAZ;;AACA,eAAKN,MAAL,CAAY2B,IAAZ;;AACA,eAAKzB,cAAL,GAAsB,KAAtB;AACH;AAED;AACJ;AACA;;;AACW0B,QAAAA,UAAU,GAAS;AACtB,cAAI,CAAC,KAAK5B,MAAN,IAAgB,CAAC,KAAKE,cAA1B,EAA0C;AACtCG,YAAAA,OAAO,CAACiB,IAAR,CAAa,iDAAb;AACA;AACH;;AAED,eAAKtB,MAAL,CAAY6B,KAAZ;;AACA,eAAK1B,SAAL,GAAiB,IAAjB;AACAE,UAAAA,OAAO,CAACC,GAAR,CAAY,yBAAZ;AACH;AAED;AACJ;AACA;;;AACWwB,QAAAA,WAAW,GAAS;AACvB,cAAI,CAAC,KAAK9B,MAAN,IAAgB,CAAC,KAAKE,cAA1B,EAA0C;AACtCG,YAAAA,OAAO,CAACiB,IAAR,CAAa,kDAAb;AACA;AACH;;AAED,eAAKtB,MAAL,CAAYc,KAAZ;;AACA,eAAKX,SAAL,GAAiB,KAAjB;AACAE,UAAAA,OAAO,CAACC,GAAR,CAAY,0BAAZ;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWC,QAAAA,cAAc,CAACwB,MAAD,EAA0B;AAC3C,cAAI,CAAC,KAAK/B,MAAV,EAAkB;AACdK,YAAAA,OAAO,CAACiB,IAAR,CAAa,uDAAb;AACA,mBAAO,KAAP;AACH;;AAED,iBAAO,KAAKtB,MAAL,CAAYO,cAAZ,CAA2BwB,MAA3B,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWC,QAAAA,gBAAgB,CAAmBC,iBAAnB,EAA0E;AAC7F,cAAI,CAAC,KAAKjC,MAAV,EAAkB;AACdK,YAAAA,OAAO,CAACiB,IAAR,CAAa,yDAAb;AACA,mBAAO,KAAP;AACH;;AAED,iBAAO,KAAKtB,MAAL,CAAYgC,gBAAZ,CAA6BC,iBAA7B,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWC,QAAAA,SAAS,CAAmBD,iBAAnB,EAA2E;AACvF,cAAI,CAAC,KAAKjC,MAAV,EAAkB;AACdK,YAAAA,OAAO,CAACiB,IAAR,CAAa,kDAAb;AACA,mBAAO,IAAP;AACH;;AAED,iBAAO,KAAKtB,MAAL,CAAYkC,SAAZ,CAAyBD,iBAAzB,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACWE,QAAAA,QAAQ,GAAiB;AAC5B,iBAAO,KAAKnC,MAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACWoC,QAAAA,kBAAkB,GAAY;AACjC,iBAAO,KAAKlC,cAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACWmC,QAAAA,aAAa,GAAY;AAC5B,iBAAO,KAAKlC,SAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACWmC,QAAAA,aAAa,GAAsB;AACtC,iBAAO,KAAKtC,MAAL,GAAc,KAAKA,MAAL,CAAYuC,QAAZ,EAAd,GAAuC,IAA9C;AACH;AAED;AACJ;AACA;;;AACY1B,QAAAA,sBAAsB,GAAS;AACnC;AACA,gBAAM2B,MAAqC,GAAG;AAC1CC,YAAAA,MAAM,EAAE,KAAKC,QAD6B;AAE1CC,YAAAA,OAAO,EAAE,KAAKA,OAF4B;AAG1CC,YAAAA,UAAU,EAAE,KAAKA,UAHyB;AAI1CC,YAAAA,UAAU,EAAE,KAAKA,UAAL,GAAkB,CAAlB,GAAsB,KAAKA,UAA3B,GAAwCC,IAAI,CAACC,GAAL,EAJV;AAK1CC,YAAAA,aAAa,EAAE;AACXC,cAAAA,OAAO,EAAE,CAAC,GADC;AAEXC,cAAAA,SAAS,EAAE,KAAKA,SAFL;AAGXC,cAAAA,WAAW,EAAE,IAHF;AAIXC,cAAAA,eAAe,EAAE;AAJN,aAL2B;AAW1CC,YAAAA,YAAY,EAAE;AACVC,cAAAA,eAAe,EAAE,IADP;AAEVC,cAAAA,YAAY,EAAE,KAAKA,YAFT;AAGVC,cAAAA,oBAAoB,EAAE,IAHZ;AAIVC,cAAAA,WAAW,EAAE;AAJH,aAX4B;AAiB1CC,YAAAA,UAAU,EAAE;AACRC,cAAAA,eAAe,EAAE,KAAKC,WADd;AAERC,cAAAA,mBAAmB,EAAE,KAAKD,WAFlB;AAGRE,cAAAA,oBAAoB,EAAE,KAAKF,WAHnB;AAIRG,cAAAA,gBAAgB,EAAE,KAAKH;AAJf;AAjB8B,WAA9C,CAFmC,CA2BnC;;AACA,eAAK3D,eAAL,GAAuB;AAAA;AAAA,0DAAwBuC,MAAxB,CAAvB;AAEAnC,UAAAA,OAAO,CAACC,GAAR,CAAY,yCAAZ,EAAuD,KAAKL,eAA5D;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACYQ,QAAAA,oBAAoB,CAACuD,QAAD,EAAuBC,QAAvB,EAAmD;AAC3E5D,UAAAA,OAAO,CAACC,GAAR,CAAa,uCAAsC0D,QAAS,OAAMC,QAAS,EAA3E,EAD2E,CAG3E;;AACA,kBAAQA,QAAR;AACI,iBAAK;AAAA;AAAA,0CAAWC,OAAhB;AACI,mBAAK/D,SAAL,GAAiB,KAAjB;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWgE,MAAhB;AACI,mBAAKhE,SAAL,GAAiB,IAAjB;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWiE,OAAhB;AACI,mBAAKlE,cAAL,GAAsB,KAAtB;AACA,mBAAKC,SAAL,GAAiB,KAAjB;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWkE,KAAhB;AACI,mBAAKnE,cAAL,GAAsB,KAAtB;AACA,mBAAKC,SAAL,GAAiB,KAAjB;AACA;AAdR;AAgBH;AAED;AACJ;AACA;AACA;;;AACYS,QAAAA,aAAa,CAACW,KAAD,EAAqB;AACtClB,UAAAA,OAAO,CAACkB,KAAR,CAAc,kCAAd,EAAkDA,KAAlD,EADsC,CAGtC;AACA;AACH;;AA/ToC,O;;;;;iBAGT,I;;;;;;;iBAGA;AAAA;AAAA,oCAAS+C,K;;;;;;;iBAGZ,W;;;;;;;iBAGY;AAAA;AAAA,kDAAgBC,M;;;;;;;iBAGzB,C;;;;;;;iBAGD,G;;;;;;;iBAGG,K;;;;;;;iBAGA,I", "sourcesContent": ["import { _decorator, Component, Node, Enum } from \"cc\";\r\nimport { World, WorldState } from \"./base/World\";\r\nimport { IWorldInitializeData, WorldInitializeData, GameMode, DifficultyLevel } from \"./WorldInitializeData\";\r\nimport { System } from \"./base/System\";\r\nimport { LevelSystem } from \"./level/LevelSystem\";\r\nconst { ccclass, property } = _decorator;\r\n\r\n/**\r\n * Bootstrap component - the main entry point for the World system\r\n * Extends cc.Component to integrate with Cocos Creator's lifecycle\r\n * Creates and manages the World instance and drives the world update loop\r\n */\r\n@ccclass(\"Bootstrap\")\r\nexport class Bootstrap extends Component {\r\n\r\n    @property({ displayName: \"Auto Start\", tooltip: \"Automatically start the world on component start\" })\r\n    public autoStart: boolean = true;\r\n\r\n    @property({ displayName: \"Game Mode\", type: Enum(GameMode), tooltip: \"The game mode to initialize\" })\r\n    public gameMode: GameMode = GameMode.STORY;\r\n\r\n    @property({ displayName: \"Level ID\", tooltip: \"The level identifier to load\" })\r\n    public levelId: string = \"level_001\";\r\n\r\n    @property({ displayName: \"Difficulty\", type: Enum(DifficultyLevel), tooltip: \"The difficulty level\" })\r\n    public difficulty: DifficultyLevel = DifficultyLevel.NORMAL;\r\n\r\n    @property({ displayName: \"Random Seed\", tooltip: \"Random seed for deterministic gameplay (0 = use current time)\" })\r\n    public randomSeed: number = 0;\r\n\r\n    @property({ displayName: \"Time Scale\", range: [0.1, 5.0, 0.1], tooltip: \"Time scale for the world (1.0 = normal speed)\" })\r\n    public timeScale: number = 1.0;\r\n\r\n    @property({ displayName: \"Enable Debug\", tooltip: \"Enable debug features\" })\r\n    public enableDebug: boolean = false;\r\n\r\n    @property({ displayName: \"Max Particles\", tooltip: \"Maximum number of particles\" })\r\n    public maxParticles: number = 1000;\r\n\r\n    // World instance\r\n    private _world: World | null = null;\r\n\r\n    // Initialization data\r\n    private _initializeData: WorldInitializeData | null = null;\r\n\r\n    // State tracking\r\n    private _isInitialized: boolean = false;\r\n    private _isPaused: boolean = false;\r\n\r\n    /**\r\n     * Component lifecycle - called when component is loaded\r\n     */\r\n    onLoad(): void {\r\n        console.log(\"Bootstrap: Component loaded\");\r\n\r\n        // Create world instance\r\n        this._world = new World();\r\n        this._world.registerSystem(new LevelSystem());\r\n\r\n        // Set up world event callbacks\r\n        this._world.setOnStateChanged(this._onWorldStateChanged.bind(this));\r\n        this._world.setOnError(this._onWorldError.bind(this));\r\n\r\n        // Prepare initialization data\r\n        this._prepareInitializeData();\r\n    }\r\n\r\n    /**\r\n     * Component lifecycle - called when component starts\r\n     */\r\n    start(): void {\r\n        console.log(\"Bootstrap: Component started\");\r\n\r\n        if (this.autoStart) {\r\n            this.startWorld();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Component lifecycle - called every frame\r\n     */\r\n    update(deltaTime: number): void {\r\n        if (!this._world || !this._isInitialized || this._isPaused) {\r\n            return;\r\n        }\r\n\r\n        // Update the world\r\n        this._world.update(deltaTime);\r\n    }\r\n\r\n    /**\r\n     * Component lifecycle - called after all updates\r\n     */\r\n    lateUpdate(): void {\r\n        if (!this._world || !this._isInitialized || this._isPaused) {\r\n            return;\r\n        }\r\n\r\n        // Late update the world\r\n        this._world.lateUpdate(0); // LateUpdate doesn't need deltaTime in this context\r\n    }\r\n\r\n    /**\r\n     * Component lifecycle - called when component is destroyed\r\n     */\r\n    onDestroy(): void {\r\n        console.log(\"Bootstrap: Component destroyed\");\r\n\r\n        if (this._world) {\r\n            this._world.destroy();\r\n            this._world = null;\r\n        }\r\n\r\n        this._isInitialized = false;\r\n    }\r\n\r\n    /**\r\n     * Start the world with the configured settings\r\n     * @returns Promise that resolves when the world is started\r\n     */\r\n    public async startWorld(): Promise<boolean> {\r\n        if (!this._world || this._isInitialized) {\r\n            console.warn(\"Bootstrap: Cannot start world - already initialized or no world instance\");\r\n            return false;\r\n        }\r\n\r\n        if (!this._initializeData) {\r\n            console.error(\"Bootstrap: Cannot start world - no initialization data\");\r\n            return false;\r\n        }\r\n\r\n        console.log(\"Bootstrap: Starting world...\");\r\n\r\n        const success = await this._world.initialize(this._initializeData);\r\n        if (success) {\r\n            this._isInitialized = true;\r\n            console.log(\"Bootstrap: World started successfully\");\r\n        } else {\r\n            console.error(\"Bootstrap: Failed to start world\");\r\n        }\r\n\r\n        return success;\r\n    }\r\n\r\n    /**\r\n     * Stop the world\r\n     */\r\n    public stopWorld(): void {\r\n        if (!this._world || !this._isInitialized) {\r\n            console.warn(\"Bootstrap: Cannot stop world - not initialized\");\r\n            return;\r\n        }\r\n\r\n        console.log(\"Bootstrap: Stopping world...\");\r\n        this._world.stop();\r\n        this._isInitialized = false;\r\n    }\r\n\r\n    /**\r\n     * Pause the world\r\n     */\r\n    public pauseWorld(): void {\r\n        if (!this._world || !this._isInitialized) {\r\n            console.warn(\"Bootstrap: Cannot pause world - not initialized\");\r\n            return;\r\n        }\r\n\r\n        this._world.pause();\r\n        this._isPaused = true;\r\n        console.log(\"Bootstrap: World paused\");\r\n    }\r\n\r\n    /**\r\n     * Resume the world\r\n     */\r\n    public resumeWorld(): void {\r\n        if (!this._world || !this._isInitialized) {\r\n            console.warn(\"Bootstrap: Cannot resume world - not initialized\");\r\n            return;\r\n        }\r\n\r\n        this._world.start();\r\n        this._isPaused = false;\r\n        console.log(\"Bootstrap: World resumed\");\r\n    }\r\n\r\n    /**\r\n     * Register a system to the world\r\n     * @param system The system to register\r\n     * @returns true if registration was successful\r\n     */\r\n    public registerSystem(system: System): boolean {\r\n        if (!this._world) {\r\n            console.warn(\"Bootstrap: Cannot register system - no world instance\");\r\n            return false;\r\n        }\r\n\r\n        return this._world.registerSystem(system);\r\n    }\r\n\r\n    /**\r\n     * Unregister a system from the world\r\n     * @param systemConstructor The constructor of the system to unregister\r\n     * @returns true if unregistration was successful\r\n     */\r\n    public unregisterSystem<T extends System>(systemConstructor: new (...args: any[]) => T): boolean {\r\n        if (!this._world) {\r\n            console.warn(\"Bootstrap: Cannot unregister system - no world instance\");\r\n            return false;\r\n        }\r\n\r\n        return this._world.unregisterSystem(systemConstructor);\r\n    }\r\n\r\n    /**\r\n     * Get a system by type\r\n     * @param systemConstructor The constructor of the system to get\r\n     * @returns The system instance or null if not found\r\n     */\r\n    public getSystem<T extends System>(systemConstructor: new (...args: any[]) => T): T | null {\r\n        if (!this._world) {\r\n            console.warn(\"Bootstrap: Cannot get system - no world instance\");\r\n            return null;\r\n        }\r\n\r\n        return this._world.getSystem<T>(systemConstructor);\r\n    }\r\n\r\n    /**\r\n     * Get the world instance\r\n     * @returns The world instance or null if not created\r\n     */\r\n    public getWorld(): World | null {\r\n        return this._world;\r\n    }\r\n\r\n    /**\r\n     * Check if the world is initialized\r\n     * @returns true if the world is initialized\r\n     */\r\n    public isWorldInitialized(): boolean {\r\n        return this._isInitialized;\r\n    }\r\n\r\n    /**\r\n     * Check if the world is paused\r\n     * @returns true if the world is paused\r\n     */\r\n    public isWorldPaused(): boolean {\r\n        return this._isPaused;\r\n    }\r\n\r\n    /**\r\n     * Get the current world state\r\n     * @returns The current world state or null if no world\r\n     */\r\n    public getWorldState(): WorldState | null {\r\n        return this._world ? this._world.getState() : null;\r\n    }\r\n\r\n    /**\r\n     * Prepare the initialization data from component properties\r\n     */\r\n    private _prepareInitializeData(): void {\r\n        // Create configuration object\r\n        const config: Partial<IWorldInitializeData> = {\r\n            modeId: this.gameMode,\r\n            levelId: this.levelId,\r\n            difficulty: this.difficulty,\r\n            randomSeed: this.randomSeed > 0 ? this.randomSeed : Date.now(),\r\n            physicsConfig: {\r\n                gravity: -9.8,\r\n                timeScale: this.timeScale,\r\n                maxVelocity: 1000,\r\n                enableCollision: true\r\n            },\r\n            renderConfig: {\r\n                enableParticles: true,\r\n                maxParticles: this.maxParticles,\r\n                enablePostProcessing: true,\r\n                renderScale: 1.0\r\n            },\r\n            debugFlags: {\r\n                enableDebugDraw: this.enableDebug,\r\n                showCollisionBounds: this.enableDebug,\r\n                showPerformanceStats: this.enableDebug,\r\n                logSystemUpdates: this.enableDebug\r\n            }\r\n        };\r\n\r\n        // Create initialization data with configuration\r\n        this._initializeData = new WorldInitializeData(config);\r\n\r\n        console.log(\"Bootstrap: Initialization data prepared\", this._initializeData);\r\n    }\r\n\r\n    /**\r\n     * Handle world state changes\r\n     * @param oldState The previous world state\r\n     * @param newState The new world state\r\n     */\r\n    private _onWorldStateChanged(oldState: WorldState, newState: WorldState): void {\r\n        console.log(`Bootstrap: World state changed from ${oldState} to ${newState}`);\r\n\r\n        // Handle specific state transitions\r\n        switch (newState) {\r\n            case WorldState.RUNNING:\r\n                this._isPaused = false;\r\n                break;\r\n            case WorldState.PAUSED:\r\n                this._isPaused = true;\r\n                break;\r\n            case WorldState.STOPPED:\r\n                this._isInitialized = false;\r\n                this._isPaused = false;\r\n                break;\r\n            case WorldState.ERROR:\r\n                this._isInitialized = false;\r\n                this._isPaused = false;\r\n                break;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Handle world errors\r\n     * @param error The error that occurred\r\n     */\r\n    private _onWorldError(error: Error): void {\r\n        console.error(\"Bootstrap: World error occurred:\", error);\r\n\r\n        // You can add custom error handling here\r\n        // For example, show error UI, restart world, etc.\r\n    }\r\n}"]}