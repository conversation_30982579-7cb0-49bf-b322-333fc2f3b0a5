{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/weapon/Weapon.ts"], "names": ["_decorator", "CObject", "ccclass", "property", "Weapon"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;wBAGRI,M,WADrBF,OAAO,CAAC,SAAD,C,gBAAR,MACsBE,MADtB;AAAA;AAAA,8BAC6C,E", "sourcesContent": ["import { _decorator, Node, Prefab } from 'cc';\r\nimport { CObject } from '../base/Object';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('Emitter')\r\nexport abstract class Weapon extends CObject {\r\n\r\n}"]}