System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Color, Vec3, GizmoDrawer, RegisterGizmoDrawer, GizmoUtils, EmitterArc, _dec, _class, _crd, EmitterArcGizmo;

  function _reportPossibleCrUseOfGizmoDrawer(extras) {
    _reporterNs.report("GizmoDrawer", "./GizmoDrawer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRegisterGizmoDrawer(extras) {
    _reporterNs.report("RegisterGizmoDrawer", "./GizmoDrawer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGizmoUtils(extras) {
    _reporterNs.report("GizmoUtils", "./GizmoUtils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterArc(extras) {
    _reporterNs.report("EmitterArc", "../world/weapon/EmitterArc", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Color = _cc.Color;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      GizmoDrawer = _unresolved_2.GizmoDrawer;
      RegisterGizmoDrawer = _unresolved_2.RegisterGizmoDrawer;
    }, function (_unresolved_3) {
      GizmoUtils = _unresolved_3.GizmoUtils;
    }, function (_unresolved_4) {
      EmitterArc = _unresolved_4.EmitterArc;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3c748JAhQlND5BFXS223daj", "EmitterArcGizmo", undefined);

      __checkObsolete__(['_decorator', 'Graphics', 'Color', 'Node', 'Vec3']);

      /**
       * Gizmo drawer for EmitterArc components
       * Draws visual debugging information for arc-based bullet emitters
       */
      _export("EmitterArcGizmo", EmitterArcGizmo = (_dec = _crd && RegisterGizmoDrawer === void 0 ? (_reportPossibleCrUseOfRegisterGizmoDrawer({
        error: Error()
      }), RegisterGizmoDrawer) : RegisterGizmoDrawer, _dec(_class = class EmitterArcGizmo extends (_crd && GizmoDrawer === void 0 ? (_reportPossibleCrUseOfGizmoDrawer({
        error: Error()
      }), GizmoDrawer) : GizmoDrawer) {
        constructor(...args) {
          super(...args);
          this.componentType = _crd && EmitterArc === void 0 ? (_reportPossibleCrUseOfEmitterArc({
            error: Error()
          }), EmitterArc) : EmitterArc;
          this.drawerName = "EmitterArcGizmo";
          // Gizmo display options
          this.showRadius = true;
          this.showDirections = true;
          this.showCenter = true;
          this.showArc = true;
          // Colors
          this.radiusColor = Color.GRAY;
          this.directionColor = Color.RED;
          this.centerColor = Color.WHITE;
          this.arcColor = Color.YELLOW;
          // Display settings
          this.speedScale = 1.0;
          this.arrowSize = 8;
          this.centerSize = 8;
        }

        drawGizmos(emitter, graphics, node) {
          // For 2D projects, convert world position to graphics local space
          const gizmoPos = this.worldToGizmoSpace(node.worldPosition, graphics.node);
          const gizmoX = gizmoPos.x;
          const gizmoY = gizmoPos.y; // Draw center point

          if (this.showCenter) {
            this.drawCenter(graphics, gizmoX, gizmoY);
          } // Draw radius circle


          if (this.showRadius && emitter.radius > 0) {
            this.drawRadius(graphics, gizmoX, gizmoY, emitter.radius);
          } // Draw arc indicator


          if (this.showArc && emitter.arc > 0) {
            this.drawArcIndicator(graphics, emitter, gizmoX, gizmoY);
          } // Draw direction arrows


          if (this.showDirections && emitter.count > 0) {
            this.drawDirections(graphics, emitter, gizmoX, gizmoY);
          }
        }
        /**
         * Convert world position to gizmo graphics coordinate space
         * For 2D projects, this converts world coordinates to the local space of the graphics node
         */


        worldToGizmoSpace(worldPos, gizmoNode) {
          // Convert world position to local position of the gizmo graphics node
          const localPos = new Vec3();
          gizmoNode.inverseTransformPoint(localPos, worldPos);
          return {
            x: localPos.x,
            y: localPos.y
          };
        }

        drawCenter(graphics, worldX, worldY) {
          (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
            error: Error()
          }), GizmoUtils) : GizmoUtils).drawCross(graphics, worldX, worldY, this.centerSize, this.centerColor);
        }

        drawRadius(graphics, worldX, worldY, radius) {
          (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
            error: Error()
          }), GizmoUtils) : GizmoUtils).drawCircle(graphics, worldX, worldY, radius, this.radiusColor, false);
        }

        drawArcIndicator(graphics, emitter, worldX, worldY) {
          if (emitter.arc <= 0) return;
          graphics.strokeColor = this.arcColor;
          graphics.lineWidth = 2; // Convert angle and arc to radians
          // Use the same coordinate system as EmitterArc.getDirection() - no +90 offset

          const baseAngleRad = emitter.angle * Math.PI / 180;
          const arcRad = emitter.arc * Math.PI / 180;
          const startAngle = baseAngleRad - arcRad / 2;
          const endAngle = baseAngleRad + arcRad / 2; // Draw arc starting from the emitter radius (spawn position) extending outward

          const arcStartRadius = emitter.radius; // Start from spawn radius
          // Use same length calculation as direction arrows for consistency

          const baseLength = 30;
          const speedFactor = emitter.speedMultiplier || 1;
          const arcLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);
          const arcEndRadius = arcStartRadius + arcLength; // Draw arc lines from spawn radius extending outward to show direction range

          const segments = Math.max(8, Math.floor(emitter.arc / 5)); // More segments for larger arcs
          // Draw the arc at the end radius to show the direction spread

          for (let i = 0; i <= segments; i++) {
            const angle = startAngle + (endAngle - startAngle) * (i / segments);
            const x = worldX + Math.cos(angle) * arcEndRadius;
            const y = worldY + Math.sin(angle) * arcEndRadius;

            if (i === 0) {
              graphics.moveTo(x, y);
            } else {
              graphics.lineTo(x, y);
            }
          } // Draw lines from spawn position to end of arc to show the direction range


          const startSpawnX = worldX + Math.cos(startAngle) * arcStartRadius;
          const startSpawnY = worldY + Math.sin(startAngle) * arcStartRadius;
          const endSpawnX = worldX + Math.cos(endAngle) * arcStartRadius;
          const endSpawnY = worldY + Math.sin(endAngle) * arcStartRadius;
          const startEndX = worldX + Math.cos(startAngle) * arcEndRadius;
          const startEndY = worldY + Math.sin(startAngle) * arcEndRadius;
          const endEndX = worldX + Math.cos(endAngle) * arcEndRadius;
          const endEndY = worldY + Math.sin(endAngle) * arcEndRadius; // Draw lines from spawn radius to end radius for arc boundaries

          graphics.moveTo(startSpawnX, startSpawnY);
          graphics.lineTo(startEndX, startEndY);
          graphics.moveTo(endSpawnX, endSpawnY);
          graphics.lineTo(endEndX, endEndY);
          graphics.stroke();
        }

        drawDirections(graphics, emitter, worldX, worldY) {
          const baseLength = 30;
          const speedFactor = emitter.speedMultiplier || 1;
          const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);

          for (let i = 0; i < emitter.count; i++) {
            const direction = emitter.getDirection(i);
            const spawnPos = emitter.getSpawnPosition(i); // Start position (at spawn position relative to world position)

            const startX = worldX + spawnPos.x;
            const startY = worldY + spawnPos.y; // End position (direction from spawn position)

            const endX = startX + direction.x * arrowLength;
            const endY = startY + direction.y * arrowLength; // Draw arrow

            (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
              error: Error()
            }), GizmoUtils) : GizmoUtils).drawArrow(graphics, startX, startY, endX, endY, this.directionColor, this.arrowSize);
          }
        }

        getPriority() {
          return 10; // Draw emitter gizmos with medium priority
        }
        /**
         * Configure display options
         */


        configure(options) {
          if (options.showRadius !== undefined) this.showRadius = options.showRadius;
          if (options.showDirections !== undefined) this.showDirections = options.showDirections;
          if (options.showCenter !== undefined) this.showCenter = options.showCenter;
          if (options.showArc !== undefined) this.showArc = options.showArc;
          if (options.radiusColor !== undefined) this.radiusColor = options.radiusColor;
          if (options.directionColor !== undefined) this.directionColor = options.directionColor;
          if (options.centerColor !== undefined) this.centerColor = options.centerColor;
          if (options.arcColor !== undefined) this.arcColor = options.arcColor;
          if (options.speedScale !== undefined) this.speedScale = options.speedScale;
          if (options.arrowSize !== undefined) this.arrowSize = options.arrowSize;
          if (options.centerSize !== undefined) this.centerSize = options.centerSize;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c02c0d4d92b525c8ffe3aef04c2899b33ea9c8ba.js.map