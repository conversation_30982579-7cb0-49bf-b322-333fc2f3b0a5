System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, CObject, _dec, _class, _crd, ccclass, property, Weapon;

  function _reportPossibleCrUseOfCObject(extras) {
    _reporterNs.report("CObject", "../base/Object", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      CObject = _unresolved_2.CObject;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e5b0edKuXxOzaQxHWU9ur73", "Weapon", undefined);

      __checkObsolete__(['_decorator', 'Node', 'Prefab']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("Weapon", Weapon = (_dec = ccclass('Emitter'), _dec(_class = class Weapon extends (_crd && CObject === void 0 ? (_reportPossibleCrUseOfCObject({
        error: Error()
      }), CObject) : CObject) {}) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b01855746d508db455da3edab80e5bef9667c6e3.js.map